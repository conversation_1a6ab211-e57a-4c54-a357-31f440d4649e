import { Inject, <PERSON>ton } from '../../core/ioc';
import { LogClass, LogLevel } from '../../core/logging';
import { NotFoundError } from '../../core/errors';
import { UserManager, UserAliasType } from '../../user';
import { NFTManager } from '../../nft/nft-manager';
import { BlockchainNetwork } from '../../blockchain';
import { NFT } from '../nft';

export interface NFTOwnershipRequirement {
    contractAddress: string;
    network: BlockchainNetwork;
}

export interface NFTOwnershipResult {
    requirement: NFTOwnershipRequirement;
    passed: boolean;
}

@Singleton
@LogClass({ level: LogLevel.Info })
export class NFTOwnershipChecker {
    constructor(
        @Inject private readonly userManager: UserManager,
        @Inject private readonly nftManager: NFTManager) {
    }

    public async checkAll(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<boolean> {
        if (!requirements.length)
            return true;

        const results = await this.check(userId, ...requirements);
        return results.every(r => r.passed);
    }

    public async checkAny(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<boolean> {
        if (!requirements.length)
            return true;

        const results = await this.check(userId, ...requirements);
        return results.some(r => r.passed);
    }

    private async check(userId: number, ...requirements: NFTOwnershipRequirement[]): Promise<NFTOwnershipResult[]> {
        const user = await this.userManager.get(userId);

        if (!user)
            throw new NotFoundError(`User ${userId} not found.`);

        const ethereumAddress = user.aliases.find(a => a.type === UserAliasType.EthereumAddress)?.value;

        if (!ethereumAddress)
            throw new NotFoundError(`Alias for user ${userId} not found.`);

        let results: NFTOwnershipResult[] = [];

        for (const req of requirements) {
            const ownedNfts = await this.nftManager.getOwnerTokens(ethereumAddress, req.contractAddress, req.network);

            results.push({
                requirement: req,
                passed: ownedNfts.length > 0
            });
        }

        return results;
    }

    private async checkOwnerTokens(address: string, requirement: NFTOwnershipRequirement): Promise<NFT[]> {
        const ownedNfts = await this.nftManager.getOwnerTokens(address, requirement.contractAddress, requirement.network);
    }
}